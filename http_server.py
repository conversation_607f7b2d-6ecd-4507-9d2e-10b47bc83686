#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTTP服务器模块
用于提供知识图谱可视化文件的Web访问
"""

import os
import threading
import http.server
import socketserver
import socket
from typing import Optional


class KnowledgeGraphHTTPServer:
    """知识图谱HTTP服务器"""

    def __init__(self, port: int = 8000, directory: str = "."):
        self.port = port
        self.directory = os.path.abspath(directory)
        self.server = None
        self.thread = None
        self.running = False

    def find_available_port(self, start_port: int = 8000) -> int:
        """找到可用的端口"""
        for port in range(start_port, start_port + 100):
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.bind(('localhost', port))
                    return port
            except OSError:
                continue
        raise RuntimeError("无法找到可用端口")

    def start_server(self) -> str:
        """启动HTTP服务器，返回访问URL"""
        if self.running:
            return f"http://localhost:{self.port}"

        # 找到可用端口
        self.port = self.find_available_port(self.port)

        # 创建自定义处理器
        directory = self.directory  # 捕获目录变量

        class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
            def __init__(self, *args, **kwargs):
                super().__init__(*args, directory=directory, **kwargs)

            def log_message(self, format, *args):
                # 静默日志输出
                pass

            def end_headers(self):
                # 添加CORS头
                self.send_header('Access-Control-Allow-Origin', '*')
                self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
                self.send_header('Access-Control-Allow-Headers', '*')
                super().end_headers()

        # 创建服务器
        try:
            self.server = socketserver.TCPServer(("localhost", self.port), CustomHTTPRequestHandler)
            self.server.allow_reuse_address = True

            # 在后台线程中运行服务器
            self.thread = threading.Thread(target=self.server.serve_forever, daemon=True)
            self.thread.start()
            self.running = True

            return f"http://localhost:{self.port}"

        except Exception as e:
            raise RuntimeError(f"启动HTTP服务器失败: {e}")

    def stop_server(self):
        """停止HTTP服务器"""
        if self.server and self.running:
            self.server.shutdown()
            self.server.server_close()
            if self.thread:
                self.thread.join(timeout=1)
            self.running = False

    def get_file_url(self, filename: str) -> str:
        """获取文件的访问URL"""
        if not self.running:
            base_url = self.start_server()
        else:
            base_url = f"http://localhost:{self.port}"

        # 确保文件名是相对于服务器目录的
        if os.path.isabs(filename):
            # 如果是绝对路径，转换为相对路径
            rel_path = os.path.relpath(filename, self.directory)
        else:
            rel_path = filename

        # 规范化路径分隔符为URL格式
        url_path = rel_path.replace(os.sep, '/')

        return f"{base_url}/{url_path}"

    def is_running(self) -> bool:
        """检查服务器是否正在运行"""
        return self.running


# 全局HTTP服务器实例
_global_server: Optional[KnowledgeGraphHTTPServer] = None


def get_global_server() -> KnowledgeGraphHTTPServer:
    """获取全局HTTP服务器实例"""
    global _global_server
    if _global_server is None:
        _global_server = KnowledgeGraphHTTPServer()
    return _global_server


def start_http_server(directory: str = ".") -> str:
    """启动HTTP服务器并返回基础URL"""
    server = get_global_server()
    server.directory = os.path.abspath(directory)
    return server.start_server()


def get_file_url(filename: str) -> str:
    """获取文件的访问URL"""
    server = get_global_server()
    return server.get_file_url(filename)


def stop_http_server():
    """停止HTTP服务器"""
    global _global_server
    if _global_server:
        _global_server.stop_server()


if __name__ == "__main__":
    # 测试HTTP服务器
    print("启动测试HTTP服务器...")

    # 创建测试HTML文件
    test_html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>测试页面</title>
    </head>
    <body>
        <h1>HTTP服务器测试成功！</h1>
        <p>这是一个测试页面，用于验证HTTP服务器功能。</p>
    </body>
    </html>
    """

    with open("test.html", "w", encoding="utf-8") as f:
        f.write(test_html)

    try:
        # 启动服务器
        base_url = start_http_server()
        print(f"服务器已启动: {base_url}")

        # 获取文件URL
        file_url = get_file_url("test.html")
        print(f"测试文件URL: {file_url}")

        print("按Enter键停止服务器...")
        input()

    finally:
        stop_http_server()
        # 清理测试文件
        if os.path.exists("test.html"):
            os.remove("test.html")
        print("服务器已停止")
