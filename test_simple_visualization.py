#!/usr/bin/env python3
"""
测试简洁版可视化功能
"""

import asyncio
import json
from kg_server import build_knowledge_graph_tool

async def test_simple_visualization():
    """测试简洁版可视化"""
    # 测试一个包含明确实体和关系的文本
    test_text = "张三是阿里巴巴公司的CEO，李四担任腾讯公司的CTO。"
    
    try:
        result = await build_knowledge_graph_tool({
            "text": test_text,
            "output_file": "simple_knowledge_graph.html"
        })
        
        result_text = result[0].text if result else "{}"
        result_data = json.loads(result_text)
        
        print(f"输入文本: {test_text}")
        print(f"处理结果: {'成功' if result_data.get('success') else '失败'}")
        
        if result_data.get('success'):
            print(f"实体数量: {result_data['stages']['knowledge_graph']['entities_count']}")
            print(f"关系数量: {result_data['stages']['knowledge_graph']['relations_count']}")
            print(f"三元组数量: {result_data['stages']['knowledge_graph']['triples_count']}")
            print(f"可视化文件: {result_data['summary']['visualization_file']}")
            
            # 检查文件是否存在
            import os
            viz_file = result_data['summary']['visualization_file']
            if os.path.exists(viz_file):
                file_size = os.path.getsize(viz_file)
                print(f"✅ 简洁版可视化文件已生成: {viz_file} ({file_size} 字节)")
                
                # 读取文件内容的前几行来验证
                with open(viz_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if "📊 概览统计" not in content and "📈 统计分析" not in content:
                        print("✅ 确认：文件中不包含统计分析内容，只有知识图谱网络图")
                    else:
                        print("⚠️ 警告：文件中仍包含统计分析内容")
            else:
                print(f"❌ 可视化文件未找到: {viz_file}")
        else:
            print(f"❌ 处理失败: {result_data.get('error', '未知错误')}")
            
        return result_data
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

async def test_empty_data():
    """测试空数据的简洁版可视化"""
    test_text = "这是一个简单的句子。"
    
    try:
        result = await build_knowledge_graph_tool({
            "text": test_text,
            "output_file": "empty_knowledge_graph.html"
        })
        
        result_text = result[0].text if result else "{}"
        result_data = json.loads(result_text)
        
        print(f"\n空数据测试:")
        print(f"输入文本: {test_text}")
        
        if result_data.get('success'):
            print("✅ 空数据处理成功，应该生成空图表显示")
        else:
            print(f"处理结果: {result_data.get('error', '未知错误')}")
            
        return result_data
        
    except Exception as e:
        print(f"空数据测试失败: {e}")
        return None

async def main():
    print("🧪 测试简洁版知识图谱可视化...")
    
    await test_simple_visualization()
    await test_empty_data()
    
    print("\n✅ 测试完成！现在你的可视化文件只包含知识图谱网络图，没有其他统计分析内容。")

if __name__ == "__main__":
    asyncio.run(main())
