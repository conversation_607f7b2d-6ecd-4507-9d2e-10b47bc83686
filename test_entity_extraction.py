#!/usr/bin/env python3
"""
测试实体提取问题
"""

import asyncio
import json
from kg_server import build_knowledge_graph_tool

async def test_entity_extraction():
    """测试实体提取"""
    test_text = "陶子芾是同济大学学生"
    
    try:
        result = await build_knowledge_graph_tool({"text": test_text})
        result_text = result[0].text if result else "{}"
        result_data = json.loads(result_text)
        
        print(f"输入文本: {test_text}")
        print(f"提取的实体: {result_data['stages']['knowledge_graph']['entities']}")
        print(f"提取的关系: {result_data['stages']['knowledge_graph']['relations']}")
        print(f"三元组数量: {result_data['stages']['knowledge_graph']['triples_count']}")
        
        if result_data['stages']['knowledge_graph']['triples_count'] == 0:
            print("\n⚠️ 没有提取到三元组，这可能是因为：")
            print("1. 实体识别不完整")
            print("2. 关系提取失败")
            print("3. 文本过于简单")
            print("\n建议尝试更复杂的文本，例如：")
            print("'陶子芾在同济大学学习计算机科学专业，他是一名优秀的学生。'")
        
        return result_data
        
    except Exception as e:
        print(f"测试失败: {e}")
        return None

async def test_complex_text():
    """测试更复杂的文本"""
    test_text = "陶子芾在同济大学学习计算机科学专业，他是一名优秀的学生。"
    
    try:
        result = await build_knowledge_graph_tool({"text": test_text})
        result_text = result[0].text if result else "{}"
        result_data = json.loads(result_text)
        
        print(f"\n复杂文本测试:")
        print(f"输入文本: {test_text}")
        print(f"提取的实体: {result_data['stages']['knowledge_graph']['entities']}")
        print(f"提取的关系: {result_data['stages']['knowledge_graph']['relations']}")
        print(f"三元组数量: {result_data['stages']['knowledge_graph']['triples_count']}")
        
        return result_data
        
    except Exception as e:
        print(f"复杂文本测试失败: {e}")
        return None

async def main():
    print("🧪 测试实体提取问题...")
    
    await test_entity_extraction()
    await test_complex_text()

if __name__ == "__main__":
    asyncio.run(main())
