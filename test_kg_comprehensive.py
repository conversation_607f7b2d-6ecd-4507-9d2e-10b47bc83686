#!/usr/bin/env python3
"""
全面测试知识图谱构建功能，包括division by zero修复验证
"""

import asyncio
import json
from kg_server import build_knowledge_graph_tool

async def test_with_real_data():
    """测试真实数据，验证可视化生成是否正常"""
    # 测试包含实体和关系的文本
    test_data = "张三是阿里巴巴公司的CEO。李四担任腾讯公司的CTO。王五在百度公司工作。"
    
    try:
        result = await build_knowledge_graph_tool({"text": test_data})
        print("✅ 真实数据测试通过")
        
        # 解析结果
        result_text = result[0].text if result else "{}"
        result_data = json.loads(result_text)
        
        if result_data.get("success"):
            print(f"📊 实体数量: {result_data['stages']['knowledge_graph']['entities_count']}")
            print(f"📊 关系数量: {result_data['stages']['knowledge_graph']['relations_count']}")
            print(f"📊 三元组数量: {result_data['stages']['knowledge_graph']['triples_count']}")
            print(f"📊 平均置信度: {result_data['stages']['knowledge_graph']['average_confidence']}")
            print(f"📄 可视化文件: {result_data['summary']['visualization_file']}")
            
            # 检查可视化文件是否生成
            import os
            viz_file = result_data['summary']['visualization_file']
            if os.path.exists(viz_file):
                print(f"✅ 可视化文件已生成: {viz_file}")
                file_size = os.path.getsize(viz_file)
                print(f"📏 文件大小: {file_size} 字节")
            else:
                print(f"❌ 可视化文件未找到: {viz_file}")
                return False
        else:
            print(f"❌ 处理失败: {result_data.get('error', '未知错误')}")
            return False
            
        return True
    except Exception as e:
        print(f"❌ 真实数据测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """运行测试"""
    print("🧪 开始全面测试知识图谱构建功能...")
    
    print(f"\n📋 运行真实数据测试...")
    result = await test_with_real_data()
    
    if result:
        print("\n🎉 测试成功！division by zero错误已修复，系统正常工作。")
    else:
        print("\n⚠️ 测试失败，需要进一步检查。")

if __name__ == "__main__":
    asyncio.run(main())
