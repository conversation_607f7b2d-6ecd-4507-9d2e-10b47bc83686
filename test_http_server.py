#!/usr/bin/env python3
"""
测试HTTP服务器功能
"""

import asyncio
import json
from kg_server import build_knowledge_graph_tool

async def test_http_url_generation():
    """测试HTTP URL生成功能"""
    test_text = "张三是阿里巴巴公司的CEO"
    
    try:
        result = await build_knowledge_graph_tool({
            "text": test_text,
            "output_file": "test_knowledge_graph.html"
        })
        
        result_text = result[0].text if result else "{}"
        result_data = json.loads(result_text)
        
        print(f"输入文本: {test_text}")
        print(f"处理结果: {'成功' if result_data.get('success') else '失败'}")
        
        if result_data.get('success'):
            viz_info = result_data['stages']['visualization']
            summary = result_data['summary']
            
            print(f"\n📄 可视化信息:")
            print(f"  本地文件: {viz_info['file_path']}")
            print(f"  访问链接: {viz_info['file_url']}")
            print(f"  文件大小: {viz_info['file_size']} 字节")
            
            print(f"\n📋 摘要信息:")
            print(f"  可视化文件: {summary['visualization_file']}")
            print(f"  可视化链接: {summary['visualization_url']}")
            
            # 检查URL格式
            url = viz_info['file_url']
            if url.startswith('http://localhost:'):
                print(f"\n✅ HTTP服务器URL生成成功！")
                print(f"🌐 你可以在浏览器中打开这个链接: {url}")
                
                # 提取端口号
                port = url.split(':')[-1].split('/')[0]
                print(f"📡 HTTP服务器运行在端口: {port}")
                
            elif url.startswith('file:///'):
                print(f"\n⚠️ 使用本地文件URL（HTTP服务器可能启动失败）: {url}")
            else:
                print(f"\n❓ 未知URL格式: {url}")
                
        else:
            print(f"❌ 处理失败: {result_data.get('error', '未知错误')}")
            
        return result_data
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

async def main():
    print("🧪 测试HTTP服务器URL生成功能...")
    
    result = await test_http_url_generation()
    
    if result and result.get('success'):
        viz_url = result['summary']['visualization_url']
        if viz_url.startswith('http://localhost:'):
            print(f"\n🎉 成功！现在你可以通过以下链接访问知识图谱可视化：")
            print(f"🔗 {viz_url}")
            print(f"\n💡 提示：")
            print(f"  - 复制链接到浏览器中打开")
            print(f"  - HTTP服务器会在后台持续运行")
            print(f"  - 支持多个文件同时访问")
        else:
            print(f"\n⚠️ HTTP服务器功能可能有问题，请检查")
    else:
        print(f"\n❌ 测试失败")

if __name__ == "__main__":
    asyncio.run(main())
