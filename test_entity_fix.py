#!/usr/bin/env python3
"""
测试实体提取修复
"""

import asyncio
import json
from kg_server import build_knowledge_graph_tool

async def test_student_text():
    """测试学生文本"""
    test_text = "陶子芾是同济大学学生"
    
    try:
        result = await build_knowledge_graph_tool({
            "text": test_text,
            "output_file": "student_knowledge_graph.html"
        })
        
        result_text = result[0].text if result else "{}"
        result_data = json.loads(result_text)
        
        print(f"输入文本: {test_text}")
        print(f"处理结果: {'成功' if result_data.get('success') else '失败'}")
        
        if result_data.get('success'):
            kg_data = result_data['stages']['knowledge_graph']
            print(f"实体数量: {kg_data['entities_count']}")
            print(f"提取的实体: {kg_data['entities']}")
            print(f"关系数量: {kg_data['relations_count']}")
            print(f"提取的关系: {kg_data['relations']}")
            print(f"三元组数量: {kg_data['triples_count']}")
            print(f"平均置信度: {kg_data['average_confidence']}")
            
            # 检查是否正确提取了实体
            entities = kg_data['entities']
            expected_entities = ["陶子芾", "同济大学", "学生"]
            found_entities = []
            
            for expected in expected_entities:
                for entity in entities:
                    if expected in entity:
                        found_entities.append(expected)
                        break
            
            print(f"期望的实体: {expected_entities}")
            print(f"找到的实体: {found_entities}")
            
            if len(found_entities) >= 2:  # 至少找到人名和学校
                print("✅ 实体提取改进成功！")
            else:
                print("⚠️ 实体提取仍需改进")
                
            # 检查可视化文件
            import os
            viz_file = result_data['summary']['visualization_file']
            if os.path.exists(viz_file):
                file_size = os.path.getsize(viz_file)
                print(f"✅ 可视化文件已生成: {viz_file} ({file_size} 字节)")
            else:
                print(f"❌ 可视化文件未找到: {viz_file}")
        else:
            print(f"❌ 处理失败: {result_data.get('error', '未知错误')}")
            
        return result_data
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

async def test_complex_student_text():
    """测试更复杂的学生文本"""
    test_text = "陶子芾是同济大学计算机科学专业的学生，他来自上海。"
    
    try:
        result = await build_knowledge_graph_tool({
            "text": test_text,
            "output_file": "complex_student_knowledge_graph.html"
        })
        
        result_text = result[0].text if result else "{}"
        result_data = json.loads(result_text)
        
        print(f"\n复杂文本测试:")
        print(f"输入文本: {test_text}")
        print(f"处理结果: {'成功' if result_data.get('success') else '失败'}")
        
        if result_data.get('success'):
            kg_data = result_data['stages']['knowledge_graph']
            print(f"实体数量: {kg_data['entities_count']}")
            print(f"提取的实体: {kg_data['entities']}")
            print(f"关系数量: {kg_data['relations_count']}")
            print(f"提取的关系: {kg_data['relations']}")
            print(f"三元组数量: {kg_data['triples_count']}")
        else:
            print(f"❌ 处理失败: {result_data.get('error', '未知错误')}")
            
        return result_data
        
    except Exception as e:
        print(f"复杂文本测试失败: {e}")
        return None

async def main():
    print("🧪 测试实体提取修复...")
    
    await test_student_text()
    await test_complex_student_text()
    
    print("\n✅ 测试完成！")

if __name__ == "__main__":
    asyncio.run(main())
