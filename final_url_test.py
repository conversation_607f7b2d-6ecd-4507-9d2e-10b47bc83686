#!/usr/bin/env python3
"""
最终测试：验证知识图谱构建和URL生成功能
"""

import asyncio
import json
import urllib.request
import urllib.error
import time
from kg_server import build_knowledge_graph_tool

async def test_complete_workflow():
    """测试完整的工作流程"""
    test_text = "陶子芾是同济大学学生"
    
    try:
        print(f"🧪 测试输入: {test_text}")
        
        # 调用知识图谱构建工具
        result = await build_knowledge_graph_tool({
            "text": test_text,
            "output_file": "final_test_kg.html"
        })
        
        result_text = result[0].text if result else "{}"
        result_data = json.loads(result_text)
        
        print(f"处理结果: {'成功' if result_data.get('success') else '失败'}")
        
        if result_data.get('success'):
            # 获取可视化信息
            viz_info = result_data['stages']['visualization']
            summary = result_data['summary']
            
            print(f"\n📊 知识图谱信息:")
            kg_data = result_data['stages']['knowledge_graph']
            print(f"  实体数量: {kg_data['entities_count']}")
            print(f"  关系数量: {kg_data['relations_count']}")
            print(f"  三元组数量: {kg_data['triples_count']}")
            print(f"  平均置信度: {kg_data['average_confidence']}")
            
            print(f"\n📄 可视化信息:")
            print(f"  本地文件: {viz_info['file_path']}")
            print(f"  访问链接: {viz_info['file_url']}")
            print(f"  文件大小: {viz_info['file_size']} 字节")
            
            # 测试URL访问
            url = viz_info['file_url']
            if url.startswith('http://localhost:'):
                print(f"\n🌐 测试URL访问...")
                
                # 等待服务器完全启动
                time.sleep(3)
                
                try:
                    with urllib.request.urlopen(url, timeout=10) as response:
                        content = response.read().decode('utf-8')
                        status_code = response.getcode()
                    
                    if status_code == 200:
                        print(f"✅ URL访问成功！")
                        print(f"📄 响应状态码: {status_code}")
                        print(f"📏 响应内容长度: {len(content)} 字符")
                        
                        # 验证内容
                        if "知识图谱可视化" in content and "Plotly" in content:
                            print(f"✅ 内容验证成功：包含知识图谱可视化")
                            return True, url
                        else:
                            print(f"⚠️ 内容验证失败：可能不是正确的知识图谱文件")
                            return False, url
                    else:
                        print(f"❌ HTTP请求失败，状态码: {status_code}")
                        return False, url
                        
                except Exception as e:
                    print(f"❌ URL访问失败: {e}")
                    print(f"💡 可能需要等待更长时间让服务器启动")
                    return False, url
            else:
                print(f"⚠️ 生成的是本地文件URL: {url}")
                return True, url
                
        else:
            print(f"❌ 处理失败: {result_data.get('error', '未知错误')}")
            return False, None
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None

async def main():
    print("🧪 最终测试：知识图谱构建和URL生成功能...")
    
    success, url = await test_complete_workflow()
    
    if success and url:
        if url.startswith('http://localhost:'):
            print(f"\n🎉 完整功能测试成功！")
            print(f"🔗 你的知识图谱可视化链接: {url}")
            print(f"\n💡 使用说明:")
            print(f"  1. 复制上面的链接到浏览器中打开")
            print(f"  2. 可以看到交互式的知识图谱网络图")
            print(f"  3. 支持拖拽节点、悬停查看详情")
            print(f"  4. HTTP服务器会在后台持续运行")
        else:
            print(f"\n✅ 知识图谱构建成功，但使用本地文件访问")
            print(f"📁 本地文件: {url}")
    else:
        print(f"\n❌ 测试失败，需要进一步检查")
    
    # 清理测试文件
    import os
    test_files = ["final_test_kg.html", "test_server.html"]
    for file in test_files:
        if os.path.exists(file):
            try:
                os.remove(file)
            except:
                pass

if __name__ == "__main__":
    asyncio.run(main())
