#!/usr/bin/env python3
"""
测试最终系统功能
"""

import asyncio
import json
from kg_server import build_knowledge_graph_tool

async def test_final_system():
    """测试最终系统"""
    test_text = "陶子芾是同济大学学生"
    
    try:
        print(f"🧪 测试输入: {test_text}")
        
        # 调用知识图谱构建工具
        result = await build_knowledge_graph_tool({
            "text": test_text
        })
        
        result_text = result[0].text if result else "{}"
        result_data = json.loads(result_text)
        
        print(f"处理结果: {'成功' if result_data.get('success') else '失败'}")
        
        if result_data.get('success'):
            # 显示完整结果
            print(f"\n📊 知识图谱信息:")
            kg_data = result_data['stages']['knowledge_graph']
            print(f"  实体数量: {kg_data['entities_count']}")
            print(f"  提取的实体: {kg_data['entities']}")
            print(f"  关系数量: {kg_data['relations_count']}")
            print(f"  提取的关系: {kg_data['relations']}")
            print(f"  三元组数量: {kg_data['triples_count']}")
            print(f"  平均置信度: {kg_data['average_confidence']}")
            
            print(f"\n📄 可视化信息:")
            viz_info = result_data['stages']['visualization']
            print(f"  本地文件: {viz_info['file_path']}")
            print(f"  访问链接: {viz_info['file_url']}")
            print(f"  服务器状态: {viz_info['server_info']}")
            print(f"  文件大小: {viz_info['file_size']} 字节")
            
            # 检查URL类型并提供相应的使用说明
            url = viz_info['file_url']
            if url.startswith('http://localhost:'):
                print(f"\n🎉 HTTP服务器成功启动！")
                print(f"🔗 可视化链接: {url}")
                print(f"\n💡 使用说明:")
                print(f"  1. 复制上面的链接到浏览器中打开")
                print(f"  2. 可以看到交互式的知识图谱网络图")
                print(f"  3. 支持拖拽节点、悬停查看详情")
            elif url.startswith('file:///'):
                print(f"\n📁 使用本地文件访问")
                print(f"🔗 本地文件链接: {url}")
                print(f"\n💡 使用说明:")
                print(f"  方式1: 直接双击文件 {viz_info['file_path']}")
                print(f"  方式2: 复制链接到浏览器地址栏")
                print(f"  方式3: {viz_info['server_info']}")
            
            return True
            
        else:
            print(f"❌ 处理失败: {result_data.get('error', '未知错误')}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    print("🧪 测试最终系统功能...")
    
    success = await test_final_system()
    
    if success:
        print(f"\n✅ 系统功能正常！")
        print(f"\n🎯 现在你可以在MCP服务器中使用以下功能:")
        print(f"  1. 输入任何文本，系统会自动构建知识图谱")
        print(f"  2. 生成简洁的HTML可视化文件（只包含知识图谱网络图）")
        print(f"  3. 提供访问链接（HTTP服务器或本地文件）")
        print(f"  4. 支持交互式图表操作")
    else:
        print(f"\n❌ 系统功能有问题，需要进一步检查")

if __name__ == "__main__":
    asyncio.run(main())
