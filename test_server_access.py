#!/usr/bin/env python3
"""
测试HTTP服务器访问
"""

import urllib.request
import urllib.error
import time
from http_server import start_http_server, get_file_url

def test_server_access():
    """测试服务器访问"""
    try:
        # 启动HTTP服务器
        print("启动HTTP服务器...")
        base_url = start_http_server()
        print(f"服务器已启动: {base_url}")

        # 等待服务器完全启动
        time.sleep(1)

        # 测试访问文件
        file_url = get_file_url("test_knowledge_graph.html")
        print(f"测试访问: {file_url}")

        # 发送HTTP请求
        with urllib.request.urlopen(file_url, timeout=5) as response:
            content = response.read().decode('utf-8')
            status_code = response.getcode()

        if status_code == 200:
            print(f"✅ HTTP服务器访问成功！")
            print(f"📄 响应状态码: {status_code}")
            print(f"📏 响应内容长度: {len(content)} 字符")
            print(f"🔗 可访问链接: {file_url}")

            # 检查内容是否包含知识图谱
            if "知识图谱可视化" in content and "Plotly" in content:
                print(f"✅ 内容验证成功：包含知识图谱可视化")
            else:
                print(f"⚠️ 内容验证失败：可能不是正确的知识图谱文件")

            return True
        else:
            print(f"❌ HTTP请求失败，状态码: {status_code}")
            return False

    except urllib.error.URLError as e:
        print(f"❌ 连接失败：HTTP服务器可能未正常启动 - {e}")
        return False
    except urllib.error.HTTPError as e:
        print(f"❌ HTTP错误：{e.code} - {e.reason}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🧪 测试HTTP服务器访问...")

    success = test_server_access()

    if success:
        print(f"\n🎉 HTTP服务器功能正常！")
        print(f"💡 现在你可以通过浏览器访问生成的知识图谱可视化链接")
    else:
        print(f"\n❌ HTTP服务器功能有问题，需要检查")
