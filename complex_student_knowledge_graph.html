
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>知识图谱可视化</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2em;
        }
        .content {
            padding: 20px;
        }
        .graph-container {
            margin: 0;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            min-height: 600px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🕸️ 知识图谱可视化</h1>
        </div>
        <div class="content">
            <div class="graph-container">
                
    <div>                        <script type="text/javascript">window.PlotlyConfig = {MathJaxConfig: 'local'};</script>
        <script charset="utf-8" src="https://cdn.plot.ly/plotly-3.0.1.min.js"></script>                <div id="graph-plot" class="plotly-graph-div" style="height:600px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("graph-plot")) {                    Plotly.newPlot(                        "graph-plot",                        [{"hoverinfo":"none","line":{"color":"#FF6B6B","width":2},"mode":"lines","name":"\u5173\u7cfb: \u4f4d\u4e8e","showlegend":true,"x":[-0.09583375252537589,-1.0,null],"y":[-0.9273222534232487,-0.23717731928349278,null],"type":"scatter"},{"hoverinfo":"none","line":{"color":"#4ECDC4","width":2},"mode":"lines","name":"\u5173\u7cfb: \u6765\u81ea","showlegend":true,"x":[-0.6389509258171175,-0.09583375252537589,null,0.7426292778326604,-0.09583375252537589,null,-0.09583375252537589,0.9921554005098329,null],"y":[0.980752458859707,-0.9273222534232487,null,0.7566008065361786,-0.9273222534232487,null,-0.9273222534232487,-0.5728536926891439,null],"type":"scatter"},{"hoverinfo":"none","line":{"color":"#45B7D1","width":2},"mode":"lines","name":"\u5173\u7cfb: \u662f","showlegend":true,"x":[-0.6389509258171175,0.7426292778326604,null,-0.6389509258171175,0.9921554005098329,null,0.7426292778326604,0.9921554005098329,null],"y":[0.980752458859707,0.7566008065361786,null,0.980752458859707,-0.5728536926891439,null,0.7566008065361786,-0.5728536926891439,null],"type":"scatter"},{"hoverinfo":"text","hovertext":["\u5b9e\u4f53: \u5b66\u751f\u003cbr\u003e\u8fde\u63a5\u6570: 3\u003cbr\u003e\u8fde\u63a5\u5230: \u9676\u5b50\u82be\u662f\u540c\u6d4e\u5927\u5b66\u8ba1\u7b97\u673a\u79d1\u5b66\u4e13\u4e1a\u7684\u5b66, \u4e0a\u6d77, \u9676\u5b50\u82be\u662f","\u5b9e\u4f53: \u9676\u5b50\u82be\u662f\u540c\u6d4e\u5927\u5b66\u8ba1\u7b97\u673a\u79d1\u5b66\u4e13\u4e1a\u7684\u5b66\u003cbr\u003e\u8fde\u63a5\u6570: 3\u003cbr\u003e\u8fde\u63a5\u5230: \u5b66\u751f, \u4e0a\u6d77, \u9676\u5b50\u82be\u662f","\u5b9e\u4f53: \u4e0a\u6d77\u003cbr\u003e\u8fde\u63a5\u6570: 4\u003cbr\u003e\u8fde\u63a5\u5230: \u5b66\u751f, \u9676\u5b50\u82be\u662f\u540c\u6d4e\u5927\u5b66\u8ba1\u7b97\u673a\u79d1\u5b66\u4e13\u4e1a\u7684\u5b66, \u9676\u5b50\u82be\u662f\u003cbr\u003e\u7b49 4 \u4e2a\u5b9e\u4f53","\u5b9e\u4f53: \u9676\u5b50\u82be\u662f\u003cbr\u003e\u8fde\u63a5\u6570: 3\u003cbr\u003e\u8fde\u63a5\u5230: \u5b66\u751f, \u9676\u5b50\u82be\u662f\u540c\u6d4e\u5927\u5b66\u8ba1\u7b97\u673a\u79d1\u5b66\u4e13\u4e1a\u7684\u5b66, \u4e0a\u6d77","\u5b9e\u4f53: \u4e2d\u56fd\u003cbr\u003e\u8fde\u63a5\u6570: 1\u003cbr\u003e\u8fde\u63a5\u5230: \u4e0a\u6d77"],"marker":{"color":["#96CEB4","#45B7D1","#96CEB4","#96CEB4","#4ECDC4"],"line":{"color":"white","width":2},"opacity":0.8,"size":[35,35,40,35,25]},"mode":"markers+text","name":"\u5b9e\u4f53","showlegend":false,"text":["\u5b66\u751f","\u9676\u5b50\u82be\u662f\u540c\u6d4e\u5927\u5b66\u8ba1\u7b97...","\u4e0a\u6d77","\u9676\u5b50\u82be\u662f","\u4e2d\u56fd"],"textfont":{"color":"white","size":10},"textposition":"middle center","x":[-0.6389509258171175,0.7426292778326604,-0.09583375252537589,0.9921554005098329,-1.0],"y":[0.980752458859707,0.7566008065361786,-0.9273222534232487,-0.5728536926891439,-0.23717731928349278],"type":"scatter"},{"hoverinfo":"text","hovertext":["\u4f4d\u4e8e\u003cbr\u003e\u7f6e\u4fe1\u5ea6: 0.950","\u6765\u81ea\u003cbr\u003e\u7f6e\u4fe1\u5ea6: 0.800","\u6765\u81ea\u003cbr\u003e\u7f6e\u4fe1\u5ea6: 0.800","\u6765\u81ea\u003cbr\u003e\u7f6e\u4fe1\u5ea6: 0.850","\u662f\u003cbr\u003e\u7f6e\u4fe1\u5ea6: 0.800","\u662f\u003cbr\u003e\u7f6e\u4fe1\u5ea6: 0.900","\u662f\u003cbr\u003e\u7f6e\u4fe1\u5ea6: 0.900"],"mode":"text","name":"\u5173\u7cfb\u6807\u7b7e","showlegend":false,"text":["\u4f4d\u4e8e","\u6765\u81ea","\u6765\u81ea","\u6765\u81ea","\u662f","\u662f","\u662f"],"textfont":{"color":"black","size":8},"x":[-0.5479168762626879,-0.3673923391712467,0.3233977626536423,0.4481608239922285,0.05183917600777144,0.17660223734635766,0.8673923391712466],"y":[-0.5822497863533708,0.026715102718229133,-0.08536072344353507,-0.7500879730561962,0.8686766326979427,0.20394938308528154,0.09187355692351734],"type":"scatter"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"font":{"size":20},"text":"\u77e5\u8bc6\u56fe\u8c31\u7f51\u7edc\u56fe","x":0.5},"margin":{"b":20,"l":5,"r":5,"t":40},"xaxis":{"showgrid":false,"zeroline":false,"showticklabels":false},"yaxis":{"showgrid":false,"zeroline":false,"showticklabels":false},"showlegend":true,"hovermode":"closest","annotations":[{"font":{"color":"gray","size":12},"showarrow":false,"text":"\u62d6\u62fd\u8282\u70b9\u53ef\u4ee5\u79fb\u52a8 | \u9f20\u6807\u60ac\u505c\u67e5\u770b\u8be6\u60c5 | \u70b9\u51fb\u56fe\u4f8b\u9690\u85cf\u002f\u663e\u793a\u5173\u7cfb","x":0.005,"xanchor":"left","xref":"paper","y":-0.002,"yanchor":"bottom","yref":"paper"}],"plot_bgcolor":"white","height":600},                        {"responsive": true}                    )                };            </script>        </div>

            </div>
        </div>
    </div>
</body>
</html>
        