#!/usr/bin/env python3
"""
测试服务器修复
"""

import asyncio
import json
from kg_server import build_knowledge_graph_tool

async def test_server_fix():
    """测试服务器修复"""
    test_text = "陶子芾是同济大学学生"
    
    try:
        print(f"🧪 测试输入: {test_text}")
        
        # 调用知识图谱构建工具
        result = await build_knowledge_graph_tool({
            "text": test_text
        })
        
        result_text = result[0].text if result else "{}"
        result_data = json.loads(result_text)
        
        print(f"处理结果: {'成功' if result_data.get('success') else '失败'}")
        
        if result_data.get('success'):
            print(f"✅ 服务器修复成功！")
            
            viz_info = result_data['stages']['visualization']
            print(f"📄 可视化文件: {viz_info['file_path']}")
            print(f"🔗 本地链接: {viz_info['file_url']}")
            print(f"🌐 HTTP链接: {viz_info['http_url']}")
            
            return True
        else:
            print(f"❌ 处理失败: {result_data.get('error', '未知错误')}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    print("🧪 测试服务器修复...")
    
    success = await test_server_fix()
    
    if success:
        print(f"\n✅ 服务器修复成功！现在可以正常使用MCP服务器了。")
    else:
        print(f"\n❌ 仍有问题需要检查")

if __name__ == "__main__":
    asyncio.run(main())
