#!/usr/bin/env python3
"""
最终测试：验证"陶子芾是同济大学学生"能正确生成可视化
"""

import asyncio
import json
from kg_server import build_knowledge_graph_tool

async def final_test():
    """最终测试"""
    test_text = "陶子芾是同济大学学生"
    
    try:
        result = await build_knowledge_graph_tool({
            "text": test_text
        })
        
        result_text = result[0].text if result else "{}"
        result_data = json.loads(result_text)
        
        print(f"输入文本: {test_text}")
        print(f"处理结果: {'成功' if result_data.get('success') else '失败'}")
        
        if result_data.get('success'):
            kg_data = result_data['stages']['knowledge_graph']
            print(f"实体数量: {kg_data['entities_count']}")
            print(f"提取的实体: {kg_data['entities']}")
            print(f"关系数量: {kg_data['relations_count']}")
            print(f"提取的关系: {kg_data['relations']}")
            print(f"三元组数量: {kg_data['triples_count']}")
            print(f"平均置信度: {kg_data['average_confidence']}")
            
            # 检查可视化文件
            import os
            viz_file = result_data['summary']['visualization_file']
            if os.path.exists(viz_file):
                file_size = os.path.getsize(viz_file)
                print(f"✅ 可视化文件已生成: {viz_file} ({file_size} 字节)")
                
                # 检查文件内容
                with open(viz_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if "暂无数据可视化" in content:
                        print("❌ 文件显示'暂无数据'，需要进一步检查")
                    elif "知识图谱网络图" in content and "Plotly" in content:
                        print("✅ 文件包含正确的知识图谱可视化")
                    else:
                        print("⚠️ 文件内容需要检查")
            else:
                print(f"❌ 可视化文件未找到: {viz_file}")
        else:
            print(f"❌ 处理失败: {result_data.get('error', '未知错误')}")
            
        return result_data
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

async def main():
    print("🧪 最终测试：验证修复效果...")
    
    result = await final_test()
    
    if result and result.get('success'):
        kg_data = result['stages']['knowledge_graph']
        if kg_data['triples_count'] > 0:
            print("\n🎉 修复成功！现在你的输入'陶子芾是同济大学学生'可以正确生成知识图谱可视化了！")
        else:
            print("\n⚠️ 虽然处理成功，但没有生成三元组，可视化可能仍显示'暂无数据'")
    else:
        print("\n❌ 修复未完全成功，需要进一步调试")

if __name__ == "__main__":
    asyncio.run(main())
