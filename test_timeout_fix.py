#!/usr/bin/env python3
"""
测试超时修复
"""

import asyncio
import json
import time
from kg_server import build_knowledge_graph_tool

async def test_timeout_fix():
    """测试超时修复"""
    test_text = "陶子芾是同济大学学生"
    
    try:
        print(f"🧪 测试输入: {test_text}")
        start_time = time.time()
        
        # 调用知识图谱构建工具
        result = await build_knowledge_graph_tool({
            "text": test_text
        })
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        result_text = result[0].text if result else "{}"
        result_data = json.loads(result_text)
        
        print(f"处理结果: {'成功' if result_data.get('success') else '失败'}")
        print(f"处理时间: {processing_time:.3f} 秒")
        
        if result_data.get('success'):
            print(f"\n📄 可视化信息:")
            viz_info = result_data['stages']['visualization']
            print(f"  本地文件: {viz_info['file_path']}")
            print(f"  本地链接: {viz_info['file_url']}")
            print(f"  HTTP链接: {viz_info['http_url']}")
            print(f"  服务器说明: {viz_info['server_info']}")
            
            print(f"\n💡 使用方法:")
            print(f"  1. 直接双击文件: {viz_info['file_path']}")
            print(f"  2. 手动启动HTTP服务器:")
            print(f"     - 在命令行运行: python -m http.server 8000")
            print(f"     - 然后访问: {viz_info['http_url']}")
            
            return True
            
        else:
            print(f"❌ 处理失败: {result_data.get('error', '未知错误')}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    print("🧪 测试超时修复...")
    
    success = await test_timeout_fix()
    
    if success:
        print(f"\n✅ 超时问题已修复！系统响应正常。")
    else:
        print(f"\n❌ 仍有问题需要检查")

if __name__ == "__main__":
    asyncio.run(main())
