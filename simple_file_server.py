#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的文件服务器
使用subprocess启动独立的HTTP服务器进程
"""

import subprocess
import time
import socket
import os
import sys
from typing import Optional


class SimpleFileServer:
    """简单文件服务器"""

    def __init__(self, port: int = 8000):
        self.port = port
        self.process: Optional[subprocess.Popen] = None
        self.running = False

    def find_available_port(self, start_port: int = 8000) -> int:
        """找到可用的端口"""
        for port in range(start_port, start_port + 100):
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.bind(('localhost', port))
                    return port
            except OSError:
                continue
        raise RuntimeError("无法找到可用端口")

    def start_server(self) -> str:
        """启动HTTP服务器"""
        if self.running:
            return f"http://localhost:{self.port}"

        # 找到可用端口
        self.port = self.find_available_port(self.port)

        try:
            # 使用Python内置的http.server模块启动服务器
            cmd = [
                sys.executable, "-m", "http.server", str(self.port),
                "--bind", "localhost"
            ]

            # 在Windows上，使用CREATE_NEW_PROCESS_GROUP避免继承控制台
            if sys.platform == "win32":
                self.process = subprocess.Popen(
                    cmd,
                    stdout=subprocess.DEVNULL,
                    stderr=subprocess.DEVNULL,
                    creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
                )
            else:
                self.process = subprocess.Popen(
                    cmd,
                    stdout=subprocess.DEVNULL,
                    stderr=subprocess.DEVNULL
                )

            # 等待服务器启动并验证
            max_attempts = 10
            for attempt in range(max_attempts):
                time.sleep(0.5)

                # 检查进程是否还在运行
                if self.process.poll() is not None:
                    raise RuntimeError("HTTP服务器进程意外退出")

                # 尝试连接服务器
                try:
                    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                        s.settimeout(1)
                        result = s.connect_ex(('localhost', self.port))
                        if result == 0:
                            self.running = True
                            return f"http://localhost:{self.port}"
                except:
                    pass

            # 如果所有尝试都失败了
            self.stop_server()
            raise RuntimeError("HTTP服务器启动超时")

        except Exception as e:
            raise RuntimeError(f"启动HTTP服务器失败: {e}")

    def stop_server(self):
        """停止HTTP服务器"""
        if self.process and self.running:
            try:
                self.process.terminate()
                self.process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.process.kill()
            except:
                pass
            finally:
                self.process = None
                self.running = False

    def get_file_url(self, filename: str) -> str:
        """获取文件的访问URL"""
        if not self.running:
            base_url = self.start_server()
        else:
            base_url = f"http://localhost:{self.port}"

        # 确保文件名是相对路径
        if os.path.isabs(filename):
            filename = os.path.basename(filename)

        # 规范化路径分隔符为URL格式
        url_path = filename.replace(os.sep, '/')

        return f"{base_url}/{url_path}"

    def is_running(self) -> bool:
        """检查服务器是否正在运行"""
        if self.process:
            return self.process.poll() is None
        return False


# 全局服务器实例
_global_file_server: Optional[SimpleFileServer] = None


def get_global_file_server() -> SimpleFileServer:
    """获取全局文件服务器实例"""
    global _global_file_server
    if _global_file_server is None:
        _global_file_server = SimpleFileServer()
    return _global_file_server


def start_file_server() -> str:
    """启动文件服务器并返回基础URL"""
    server = get_global_file_server()
    return server.start_server()


def get_file_url(filename: str) -> str:
    """获取文件的访问URL"""
    server = get_global_file_server()
    return server.get_file_url(filename)


def stop_file_server():
    """停止文件服务器"""
    global _global_file_server
    if _global_file_server:
        _global_file_server.stop_server()


if __name__ == "__main__":
    # 测试文件服务器
    print("启动测试文件服务器...")

    # 创建测试HTML文件
    test_html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>测试页面</title>
        <meta charset="UTF-8">
    </head>
    <body>
        <h1>🎉 文件服务器测试成功！</h1>
        <p>这是一个测试页面，用于验证文件服务器功能。</p>
    </body>
    </html>
    """

    with open("test_server.html", "w", encoding="utf-8") as f:
        f.write(test_html)

    try:
        # 启动服务器
        base_url = start_file_server()
        print(f"服务器已启动: {base_url}")

        # 获取文件URL
        file_url = get_file_url("test_server.html")
        print(f"测试文件URL: {file_url}")

        print("按Enter键停止服务器...")
        input()

    finally:
        stop_file_server()
        # 清理测试文件
        if os.path.exists("test_server.html"):
            os.remove("test_server.html")
        print("服务器已停止")
