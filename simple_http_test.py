#!/usr/bin/env python3
"""
简单的HTTP服务器测试
"""

import time
import urllib.request
import urllib.error
from http_server import KnowledgeGraphHTTPServer

def test_simple_server():
    """测试简单的HTTP服务器"""
    # 创建测试HTML文件
    test_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>测试页面</title>
        <meta charset="UTF-8">
    </head>
    <body>
        <h1>🎉 HTTP服务器测试成功！</h1>
        <p>这是一个测试页面，用于验证HTTP服务器功能。</p>
    </body>
    </html>
    """
    
    with open("simple_test.html", "w", encoding="utf-8") as f:
        f.write(test_content)
    
    try:
        # 创建HTTP服务器实例
        server = KnowledgeGraphHTTPServer(port=8001)
        
        print("启动HTTP服务器...")
        base_url = server.start_server()
        print(f"服务器已启动: {base_url}")
        
        # 等待服务器启动
        time.sleep(2)
        
        # 测试访问
        test_url = server.get_file_url("simple_test.html")
        print(f"测试访问: {test_url}")
        
        try:
            with urllib.request.urlopen(test_url, timeout=10) as response:
                content = response.read().decode('utf-8')
                status_code = response.getcode()
            
            if status_code == 200:
                print(f"✅ HTTP服务器访问成功！")
                print(f"📄 响应状态码: {status_code}")
                print(f"📏 响应内容长度: {len(content)} 字符")
                
                if "HTTP服务器测试成功" in content:
                    print(f"✅ 内容验证成功")
                    return True
                else:
                    print(f"⚠️ 内容验证失败")
                    return False
            else:
                print(f"❌ HTTP请求失败，状态码: {status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 访问失败: {e}")
            return False
            
    finally:
        # 清理
        try:
            server.stop_server()
            print("服务器已停止")
        except:
            pass
        
        # 删除测试文件
        import os
        if os.path.exists("simple_test.html"):
            os.remove("simple_test.html")

if __name__ == "__main__":
    print("🧪 简单HTTP服务器测试...")
    
    success = test_simple_server()
    
    if success:
        print(f"\n🎉 HTTP服务器功能正常！")
    else:
        print(f"\n❌ HTTP服务器功能有问题")
