
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>知识图谱可视化</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2em;
        }
        .content {
            padding: 20px;
        }
        .graph-container {
            margin: 0;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            min-height: 600px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🕸️ 知识图谱可视化</h1>
        </div>
        <div class="content">
            <div class="graph-container">
                
    <div>                        <script type="text/javascript">window.PlotlyConfig = {MathJaxConfig: 'local'};</script>
        <script charset="utf-8" src="https://cdn.plot.ly/plotly-3.0.1.min.js"></script>                <div id="graph-plot" class="plotly-graph-div" style="height:600px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("graph-plot")) {                    Plotly.newPlot(                        "graph-plot",                        [{"hoverinfo":"none","line":{"color":"#FF6B6B","width":2},"mode":"lines","name":"\u5173\u7cfb: \u62c5\u4efb","showlegend":true,"x":[-1.0,-0.12846206874990254,null,-1.0,0.6715963126668258,null,-0.12846206874990254,0.6715963126668258,null,-0.12846206874990254,-0.45635591233264733,null,-0.12846206874990254,0.9132216684157239,null,-0.45635591233264733,0.9132216684157239,null],"y":[-0.17534882583412165,-0.9320772671756876,null,-0.17534882583412165,0.6759968408713899,null,-0.9320772671756876,0.6759968408713899,null,-0.9320772671756876,0.8847178678452512,null,-0.9320772671756876,-0.45328861570683215,null,0.8847178678452512,-0.45328861570683215,null],"type":"scatter"},{"hoverinfo":"none","line":{"color":"#4ECDC4","width":2},"mode":"lines","name":"\u5173\u7cfb: \u62c5\u4efbCEO","showlegend":true,"x":[-1.0,-0.45635591233264733,null,-1.0,0.9132216684157239,null,0.6715963126668258,-0.45635591233264733,null,0.6715963126668258,0.9132216684157239,null],"y":[-0.17534882583412165,0.8847178678452512,null,-0.17534882583412165,-0.45328861570683215,null,0.6759968408713899,0.8847178678452512,null,0.6759968408713899,-0.45328861570683215,null],"type":"scatter"},{"hoverinfo":"text","hovertext":["\u5b9e\u4f53: \u5f20\u4e09\u662f\u963f\u003cbr\u003e\u8fde\u63a5\u6570: 4\u003cbr\u003e\u8fde\u63a5\u5230: CEO, \u674e\u56db\u62c5\u4efb, \u5f20\u4e09\u662f\u963f\u91cc\u5df4\u5df4\u516c\u53f8\u003cbr\u003e\u7b49 4 \u4e2a\u5b9e\u4f53","\u5b9e\u4f53: CEO\u003cbr\u003e\u8fde\u63a5\u6570: 4\u003cbr\u003e\u8fde\u63a5\u5230: \u5f20\u4e09\u662f\u963f, \u674e\u56db\u62c5\u4efb, \u5f20\u4e09\u662f\u963f\u91cc\u5df4\u5df4\u516c\u53f8\u003cbr\u003e\u7b49 4 \u4e2a\u5b9e\u4f53","\u5b9e\u4f53: \u674e\u56db\u62c5\u4efb\u003cbr\u003e\u8fde\u63a5\u6570: 4\u003cbr\u003e\u8fde\u63a5\u5230: \u5f20\u4e09\u662f\u963f, CEO, \u5f20\u4e09\u662f\u963f\u91cc\u5df4\u5df4\u516c\u53f8\u003cbr\u003e\u7b49 4 \u4e2a\u5b9e\u4f53","\u5b9e\u4f53: \u5f20\u4e09\u662f\u963f\u91cc\u5df4\u5df4\u516c\u53f8\u003cbr\u003e\u8fde\u63a5\u6570: 4\u003cbr\u003e\u8fde\u63a5\u5230: \u5f20\u4e09\u662f\u963f, CEO, \u674e\u56db\u62c5\u4efb\u003cbr\u003e\u7b49 4 \u4e2a\u5b9e\u4f53","\u5b9e\u4f53: \u674e\u56db\u62c5\u4efb\u817e\u8baf\u516c\u53f8\u003cbr\u003e\u8fde\u63a5\u6570: 4\u003cbr\u003e\u8fde\u63a5\u5230: \u5f20\u4e09\u662f\u963f, CEO, \u674e\u56db\u62c5\u4efb\u003cbr\u003e\u7b49 4 \u4e2a\u5b9e\u4f53"],"marker":{"color":["#FF6B6B","#96CEB4","#FF6B6B","#96CEB4","#96CEB4"],"line":{"color":"white","width":2},"opacity":0.8,"size":[40,40,40,40,40]},"mode":"markers+text","name":"\u5b9e\u4f53","showlegend":false,"text":["\u5f20\u4e09\u662f\u963f","CEO","\u674e\u56db\u62c5\u4efb","\u5f20\u4e09\u662f\u963f\u91cc\u5df4\u5df4\u516c\u53f8","\u674e\u56db\u62c5\u4efb\u817e\u8baf\u516c\u53f8"],"textfont":{"color":"white","size":10},"textposition":"middle center","x":[-1.0,-0.12846206874990254,0.6715963126668258,-0.45635591233264733,0.9132216684157239],"y":[-0.17534882583412165,-0.9320772671756876,0.6759968408713899,0.8847178678452512,-0.45328861570683215],"type":"scatter"},{"hoverinfo":"text","hovertext":["\u62c5\u4efb\u003cbr\u003e\u7f6e\u4fe1\u5ea6: 0.900","\u62c5\u4efb\u003cbr\u003e\u7f6e\u4fe1\u5ea6: 0.900","\u62c5\u4efb\u003cbr\u003e\u7f6e\u4fe1\u5ea6: 1.000","\u62c5\u4efb\u003cbr\u003e\u7f6e\u4fe1\u5ea6: 0.900","\u62c5\u4efb\u003cbr\u003e\u7f6e\u4fe1\u5ea6: 0.900","\u62c5\u4efb\u003cbr\u003e\u7f6e\u4fe1\u5ea6: 0.900","\u62c5\u4efbCEO\u003cbr\u003e\u7f6e\u4fe1\u5ea6: 0.850","\u62c5\u4efbCEO\u003cbr\u003e\u7f6e\u4fe1\u5ea6: 0.850","\u62c5\u4efbCEO\u003cbr\u003e\u7f6e\u4fe1\u5ea6: 0.850","\u62c5\u4efbCEO\u003cbr\u003e\u7f6e\u4fe1\u5ea6: 0.850"],"mode":"text","name":"\u5173\u7cfb\u6807\u7b7e","showlegend":false,"text":["\u62c5\u4efb","\u62c5\u4efb","\u62c5\u4efb","\u62c5\u4efb","\u62c5\u4efb","\u62c5\u4efb","\u62c5\u4efbCEO","\u62c5\u4efbCEO","\u62c5\u4efbCEO","\u62c5\u4efbCEO"],"textfont":{"color":"black","size":8},"x":[-0.5642310343749513,-0.16420184366658708,0.27156712195846167,-0.29240899054127495,0.3923797998329107,0.2284328780415383,-0.7281779561663236,-0.04338916579213803,0.10762020016708926,0.7924089905412749],"y":[-0.5537130465049046,0.25032400751863415,-0.12804021315214886,-0.023679699665218212,-0.6926829414412599,0.21571462606920952,0.3546845210055648,-0.3143187207704769,0.7803573543583205,0.11135411258227887],"type":"scatter"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"font":{"size":20},"text":"\u77e5\u8bc6\u56fe\u8c31\u7f51\u7edc\u56fe","x":0.5},"margin":{"b":20,"l":5,"r":5,"t":40},"xaxis":{"showgrid":false,"zeroline":false,"showticklabels":false},"yaxis":{"showgrid":false,"zeroline":false,"showticklabels":false},"showlegend":true,"hovermode":"closest","annotations":[{"font":{"color":"gray","size":12},"showarrow":false,"text":"\u62d6\u62fd\u8282\u70b9\u53ef\u4ee5\u79fb\u52a8 | \u9f20\u6807\u60ac\u505c\u67e5\u770b\u8be6\u60c5 | \u70b9\u51fb\u56fe\u4f8b\u9690\u85cf\u002f\u663e\u793a\u5173\u7cfb","x":0.005,"xanchor":"left","xref":"paper","y":-0.002,"yanchor":"bottom","yref":"paper"}],"plot_bgcolor":"white","height":600},                        {"responsive": true}                    )                };            </script>        </div>

            </div>
        </div>
    </div>
</body>
</html>
        