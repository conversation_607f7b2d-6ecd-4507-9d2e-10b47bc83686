
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>知识图谱分析报告</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        .content {
            padding: 30px;
        }
        .section {
            margin-bottom: 40px;
        }
        .section h2 {
            color: #333;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .summary-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        .summary-card h3 {
            margin: 0 0 10px 0;
            font-size: 2em;
        }
        .summary-card p {
            margin: 0;
            opacity: 0.9;
        }
        .plot-container {
            margin: 20px 0;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }
        .entity-list, .relation-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 15px;
        }
        .tag {
            background-color: #e3f2fd;
            color: #1976d2;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.9em;
            border: 1px solid #bbdefb;
        }
        .relation-tag {
            background-color: #f3e5f5;
            color: #7b1fa2;
            border-color: #ce93d8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🕸️ 知识图谱分析报告</h1>
            <p>基于三阶段MCP协议的知识图谱构建与可视化</p>
        </div>

        <div class="content">
            <div class="section">
                <h2>📊 概览统计</h2>
                <div class="summary">
                    <div class="summary-card">
                        <h3>7</h3>
                        <p>实体数量</p>
                    </div>
                    <div class="summary-card">
                        <h3>2</h3>
                        <p>关系类型</p>
                    </div>
                    <div class="summary-card">
                        <h3>51</h3>
                        <p>三元组数量</p>
                    </div>
                    <div class="summary-card">
                        <h3>0.915</h3>
                        <p>平均置信度</p>
                    </div>
                </div>
            </div>

            <div class="section">
                <h2>🕸️ 知识图谱网络图</h2>
                <div class="plot-container">
                    
    <div>                        <script type="text/javascript">window.PlotlyConfig = {MathJaxConfig: 'local'};</script>
        <script charset="utf-8" src="https://cdn.plot.ly/plotly-3.0.1.min.js"></script>                <div id="graph-plot" class="plotly-graph-div" style="height:600px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("graph-plot")) {                    Plotly.newPlot(                        "graph-plot",                        [{"hoverinfo":"none","line":{"color":"#FF6B6B","width":2},"mode":"lines","name":"\u5173\u7cfb: \u4f4d\u4e8e","showlegend":true,"x":[-0.021873571516450224,-0.3956566017047978,null,-0.021873571516450224,0.7905680978732308,null,-0.021873571516450224,0.9369238073411993,null,-0.9821396761179404,-0.7940453689046466,null,-0.9821396761179404,0.46622331302940484,null,-0.9821396761179404,0.9369238073411993,null],"y":[1.0,-0.8899025343448956,null,1.0,0.6567183836298867,null,1.0,-0.19044652989010155,null,-0.2573719143164818,0.5964820089905304,null,-0.2573719143164818,-0.9154794140689384,null,-0.2573719143164818,-0.19044652989010155,null],"type":"scatter"},{"hoverinfo":"none","line":{"color":"#4ECDC4","width":2},"mode":"lines","name":"\u5173\u7cfb: \u62c5\u4efb","showlegend":true,"x":[-0.3956566017047978,0.7905680978732308,null,-0.3956566017047978,0.9369238073411993,null,0.7905680978732308,0.9369238073411993,null,-0.7940453689046466,0.46622331302940484,null,-0.7940453689046466,0.9369238073411993,null,0.46622331302940484,0.9369238073411993,null],"y":[-0.8899025343448956,0.6567183836298867,null,-0.8899025343448956,-0.19044652989010155,null,0.6567183836298867,-0.19044652989010155,null,0.5964820089905304,-0.9154794140689384,null,0.5964820089905304,-0.19044652989010155,null,-0.9154794140689384,-0.19044652989010155,null],"type":"scatter"},{"hoverinfo":"none","line":{"color":"#45B7D1","width":2},"mode":"lines","name":"\u5173\u7cfb: \u62c5\u4efbCEO","showlegend":true,"x":[-0.021873571516450224,-0.9821396761179404,null,-0.021873571516450224,-0.7940453689046466,null,-0.021873571516450224,0.46622331302940484,null,-0.3956566017047978,-0.9821396761179404,null,-0.3956566017047978,-0.7940453689046466,null,-0.3956566017047978,0.46622331302940484,null,-0.9821396761179404,0.7905680978732308,null,0.7905680978732308,-0.7940453689046466,null,0.7905680978732308,0.46622331302940484,null],"y":[1.0,-0.2573719143164818,null,1.0,0.5964820089905304,null,1.0,-0.9154794140689384,null,-0.8899025343448956,-0.2573719143164818,null,-0.8899025343448956,0.5964820089905304,null,-0.8899025343448956,-0.9154794140689384,null,-0.2573719143164818,0.6567183836298867,null,0.6567183836298867,0.5964820089905304,null,0.6567183836298867,-0.9154794140689384,null],"type":"scatter"},{"hoverinfo":"text","hovertext":["\u5b9e\u4f53: \u738b\u4e94\u5728\u767e\u003cbr\u003e\u8fde\u63a5\u6570: 6\u003cbr\u003e\u8fde\u63a5\u5230: \u674e\u56db\u62c5\u4efb, \u738b\u4e94\u5728\u767e\u5ea6\u516c\u53f8, \u5f20\u4e09\u662f\u963f\u003cbr\u003e\u7b49 6 \u4e2a\u5b9e\u4f53","\u5b9e\u4f53: \u674e\u56db\u62c5\u4efb\u003cbr\u003e\u8fde\u63a5\u6570: 6\u003cbr\u003e\u8fde\u63a5\u5230: \u738b\u4e94\u5728\u767e, \u738b\u4e94\u5728\u767e\u5ea6\u516c\u53f8, \u5f20\u4e09\u662f\u963f\u003cbr\u003e\u7b49 6 \u4e2a\u5b9e\u4f53","\u5b9e\u4f53: \u738b\u4e94\u5728\u767e\u5ea6\u516c\u53f8\u003cbr\u003e\u8fde\u63a5\u6570: 6\u003cbr\u003e\u8fde\u63a5\u5230: \u738b\u4e94\u5728\u767e, \u674e\u56db\u62c5\u4efb, \u5f20\u4e09\u662f\u963f\u003cbr\u003e\u7b49 6 \u4e2a\u5b9e\u4f53","\u5b9e\u4f53: \u5f20\u4e09\u662f\u963f\u003cbr\u003e\u8fde\u63a5\u6570: 6\u003cbr\u003e\u8fde\u63a5\u5230: \u738b\u4e94\u5728\u767e, \u674e\u56db\u62c5\u4efb, \u738b\u4e94\u5728\u767e\u5ea6\u516c\u53f8\u003cbr\u003e\u7b49 6 \u4e2a\u5b9e\u4f53","\u5b9e\u4f53: \u5f20\u4e09\u662f\u963f\u91cc\u5df4\u5df4\u516c\u53f8\u003cbr\u003e\u8fde\u63a5\u6570: 6\u003cbr\u003e\u8fde\u63a5\u5230: \u738b\u4e94\u5728\u767e, \u674e\u56db\u62c5\u4efb, \u738b\u4e94\u5728\u767e\u5ea6\u516c\u53f8\u003cbr\u003e\u7b49 6 \u4e2a\u5b9e\u4f53","\u5b9e\u4f53: \u674e\u56db\u62c5\u4efb\u817e\u8baf\u516c\u53f8\u003cbr\u003e\u8fde\u63a5\u6570: 6\u003cbr\u003e\u8fde\u63a5\u5230: \u738b\u4e94\u5728\u767e, \u674e\u56db\u62c5\u4efb, \u738b\u4e94\u5728\u767e\u5ea6\u516c\u53f8\u003cbr\u003e\u7b49 6 \u4e2a\u5b9e\u4f53","\u5b9e\u4f53: CEO\u003cbr\u003e\u8fde\u63a5\u6570: 6\u003cbr\u003e\u8fde\u63a5\u5230: \u738b\u4e94\u5728\u767e, \u674e\u56db\u62c5\u4efb, \u738b\u4e94\u5728\u767e\u5ea6\u516c\u53f8\u003cbr\u003e\u7b49 6 \u4e2a\u5b9e\u4f53"],"marker":{"color":["#FF6B6B","#FF6B6B","#96CEB4","#FF6B6B","#96CEB4","#96CEB4","#96CEB4"],"line":{"color":"white","width":2},"opacity":0.8,"size":[50,50,50,50,50,50,50]},"mode":"markers+text","name":"\u5b9e\u4f53","showlegend":false,"text":["\u738b\u4e94\u5728\u767e","\u674e\u56db\u62c5\u4efb","\u738b\u4e94\u5728\u767e\u5ea6\u516c\u53f8","\u5f20\u4e09\u662f\u963f","\u5f20\u4e09\u662f\u963f\u91cc\u5df4\u5df4\u516c\u53f8","\u674e\u56db\u62c5\u4efb\u817e\u8baf\u516c\u53f8","CEO"],"textfont":{"color":"white","size":10},"textposition":"middle center","x":[-0.021873571516450224,-0.3956566017047978,-0.9821396761179404,0.7905680978732308,-0.7940453689046466,0.46622331302940484,0.9369238073411993],"y":[1.0,-0.8899025343448956,-0.2573719143164818,0.6567183836298867,0.5964820089905304,-0.9154794140689384,-0.19044652989010155],"type":"scatter"},{"hoverinfo":"text","hovertext":["\u4f4d\u4e8e\u003cbr\u003e\u7f6e\u4fe1\u5ea6: 0.900","\u4f4d\u4e8e\u003cbr\u003e\u7f6e\u4fe1\u5ea6: 0.900","\u4f4d\u4e8e\u003cbr\u003e\u7f6e\u4fe1\u5ea6: 0.900","\u4f4d\u4e8e\u003cbr\u003e\u7f6e\u4fe1\u5ea6: 0.900","\u4f4d\u4e8e\u003cbr\u003e\u7f6e\u4fe1\u5ea6: 0.900","\u4f4d\u4e8e\u003cbr\u003e\u7f6e\u4fe1\u5ea6: 0.900","\u62c5\u4efb\u003cbr\u003e\u7f6e\u4fe1\u5ea6: 0.900","\u62c5\u4efb\u003cbr\u003e\u7f6e\u4fe1\u5ea6: 0.900","\u62c5\u4efb\u003cbr\u003e\u7f6e\u4fe1\u5ea6: 0.900","\u62c5\u4efb\u003cbr\u003e\u7f6e\u4fe1\u5ea6: 0.900","\u62c5\u4efb\u003cbr\u003e\u7f6e\u4fe1\u5ea6: 0.900","\u62c5\u4efb\u003cbr\u003e\u7f6e\u4fe1\u5ea6: 0.900","\u62c5\u4efbCEO\u003cbr\u003e\u7f6e\u4fe1\u5ea6: 0.850","\u62c5\u4efbCEO\u003cbr\u003e\u7f6e\u4fe1\u5ea6: 0.850","\u62c5\u4efbCEO\u003cbr\u003e\u7f6e\u4fe1\u5ea6: 0.850","\u62c5\u4efbCEO\u003cbr\u003e\u7f6e\u4fe1\u5ea6: 0.850","\u62c5\u4efbCEO\u003cbr\u003e\u7f6e\u4fe1\u5ea6: 0.850","\u62c5\u4efbCEO\u003cbr\u003e\u7f6e\u4fe1\u5ea6: 0.850","\u62c5\u4efbCEO\u003cbr\u003e\u7f6e\u4fe1\u5ea6: 0.850","\u62c5\u4efbCEO\u003cbr\u003e\u7f6e\u4fe1\u5ea6: 0.850","\u62c5\u4efbCEO\u003cbr\u003e\u7f6e\u4fe1\u5ea6: 0.850"],"mode":"text","name":"\u5173\u7cfb\u6807\u7b7e","showlegend":false,"text":["\u4f4d\u4e8e","\u4f4d\u4e8e","\u4f4d\u4e8e","\u4f4d\u4e8e","\u4f4d\u4e8e","\u4f4d\u4e8e","\u62c5\u4efb","\u62c5\u4efb","\u62c5\u4efb","\u62c5\u4efb","\u62c5\u4efb","\u62c5\u4efb","\u62c5\u4efbCEO","\u62c5\u4efbCEO","\u62c5\u4efbCEO","\u62c5\u4efbCEO","\u62c5\u4efbCEO","\u62c5\u4efbCEO","\u62c5\u4efbCEO","\u62c5\u4efbCEO","\u62c5\u4efbCEO"],"textfont":{"color":"black","size":8},"x":[-0.20876508661062398,0.3843472631783903,0.45752511791237455,-0.8880925225112934,-0.25795818154426775,-0.022607934388370554,0.1974557480842165,0.27063360281820076,0.8637459526072151,-0.16391102793762088,0.07143921921827634,0.701573560185302,-0.5020066238171953,-0.4079594702105484,0.2221748707564773,-0.6888981389113691,-0.5948509853047221,0.035283355662303534,-0.0957857891223548,-0.0017386355157079092,0.6283957054513178],"y":[0.05504873282755218,0.8283591918149433,0.4047767350549492,0.16955504733702428,-0.5864256641927101,-0.22390922210329167,-0.11659207535750449,-0.5401745321174986,0.23313592686989254,-0.159498702539204,0.2030177395502144,-0.5529629719795199,0.37131404284175906,0.7982410044952652,0.0422602929655308,-0.5736372243306888,-0.14671026267718262,-0.902690974206917,0.19967323465670242,0.6266001963102086,-0.12938051521952587],"type":"scatter"}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"title":{"font":{"size":20},"text":"\u77e5\u8bc6\u56fe\u8c31\u7f51\u7edc\u56fe","x":0.5},"margin":{"b":20,"l":5,"r":5,"t":40},"xaxis":{"showgrid":false,"zeroline":false,"showticklabels":false},"yaxis":{"showgrid":false,"zeroline":false,"showticklabels":false},"showlegend":true,"hovermode":"closest","annotations":[{"font":{"color":"gray","size":12},"showarrow":false,"text":"\u62d6\u62fd\u8282\u70b9\u53ef\u4ee5\u79fb\u52a8 | \u9f20\u6807\u60ac\u505c\u67e5\u770b\u8be6\u60c5 | \u70b9\u51fb\u56fe\u4f8b\u9690\u85cf\u002f\u663e\u793a\u5173\u7cfb","x":0.005,"xanchor":"left","xref":"paper","y":-0.002,"yanchor":"bottom","yref":"paper"}],"plot_bgcolor":"white","height":600},                        {"responsive": true}                    )                };            </script>        </div>

                </div>
            </div>

            <div class="section">
                <h2>📈 统计分析</h2>
                <div class="plot-container">
                    
    <div>                        <script type="text/javascript">window.PlotlyConfig = {MathJaxConfig: 'local'};</script>
        <script charset="utf-8" src="https://cdn.plot.ly/plotly-3.0.1.min.js"></script>                <div id="stats-plot" class="plotly-graph-div" style="height:800px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("stats-plot")) {                    Plotly.newPlot(                        "stats-plot",                        [{"labels":["\u4f4d\u4e8e","\u62c5\u4efb","\u62c5\u4efbCEO"],"name":"\u5173\u7cfb\u5206\u5e03","values":[22,20,9],"type":"pie","domain":{"x":[0.0,0.45],"y":[0.625,1.0]}},{"marker":{"color":"lightblue"},"name":"\u7f6e\u4fe1\u5ea6\u5206\u5e03","x":["0.0-0.2","0.2-0.4","0.4-0.6","0.6-0.8","0.8-1.0"],"y":[0,0,0,0,51],"type":"bar","xaxis":"x","yaxis":"y"},{"marker":{"color":[0.9,1.0,0.9,1.0,1.0,1.0,0.9,1.0,0.9,1.0,1.0,1.0,0.9,0.9,0.9,0.9,0.9,0.9,0.9,0.9,1.0,1.0,1.0,1.0,0.9,0.9,0.9,0.9,0.9,0.9,0.9,0.9,0.9,0.9,0.9,0.9,0.9,0.9,0.9,0.9,0.9,0.9,0.85,0.85,0.85,0.85,0.85,0.85,0.85,0.85,0.85],"colorscale":[[0.0,"#440154"],[0.1111111111111111,"#482878"],[0.2222222222222222,"#3e4989"],[0.3333333333333333,"#31688e"],[0.4444444444444444,"#26828e"],[0.5555555555555556,"#1f9e89"],[0.6666666666666666,"#35b779"],[0.7777777777777778,"#6ece58"],[0.8888888888888888,"#b5de2b"],[1.0,"#fde725"]],"showscale":true,"size":8},"mode":"markers+lines","name":"\u7f6e\u4fe1\u5ea6\u8d8b\u52bf","x":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50],"y":[0.9,1.0,0.9,1.0,1.0,1.0,0.9,1.0,0.9,1.0,1.0,1.0,0.9,0.9,0.9,0.9,0.9,0.9,0.9,0.9,1.0,1.0,1.0,1.0,0.9,0.9,0.9,0.9,0.9,0.9,0.9,0.9,0.9,0.9,0.9,0.9,0.9,0.9,0.9,0.9,0.9,0.9,0.85,0.85,0.85,0.85,0.85,0.85,0.85,0.85,0.85],"type":"scatter","xaxis":"x2","yaxis":"y2"},{"cells":{"fill":{"color":"white"},"values":[["\u603b\u4e09\u5143\u7ec4\u6570","\u5173\u7cfb\u7c7b\u578b\u6570","\u5e73\u5747\u7f6e\u4fe1\u5ea6","\u6700\u9ad8\u7f6e\u4fe1\u5ea6","\u6700\u4f4e\u7f6e\u4fe1\u5ea6"],[51,3,"0.915","1.000","0.850"]]},"header":{"fill":{"color":"lightgray"},"values":["\u6307\u6807","\u6570\u503c"]},"type":"table","domain":{"x":[0.55,1.0],"y":[0.0,0.375]}}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"xaxis":{"anchor":"y","domain":[0.55,1.0]},"yaxis":{"anchor":"x","domain":[0.625,1.0]},"xaxis2":{"anchor":"y2","domain":[0.0,0.45]},"yaxis2":{"anchor":"x2","domain":[0.0,0.375]},"annotations":[{"font":{"size":16},"showarrow":false,"text":"\u5173\u7cfb\u7c7b\u578b\u5206\u5e03","x":0.225,"xanchor":"center","xref":"paper","y":1.0,"yanchor":"bottom","yref":"paper"},{"font":{"size":16},"showarrow":false,"text":"\u7f6e\u4fe1\u5ea6\u5206\u5e03","x":0.775,"xanchor":"center","xref":"paper","y":1.0,"yanchor":"bottom","yref":"paper"},{"font":{"size":16},"showarrow":false,"text":"\u4e09\u5143\u7ec4\u7f6e\u4fe1\u5ea6","x":0.225,"xanchor":"center","xref":"paper","y":0.375,"yanchor":"bottom","yref":"paper"},{"font":{"size":16},"showarrow":false,"text":"\u7edf\u8ba1\u6458\u8981","x":0.775,"xanchor":"center","xref":"paper","y":0.375,"yanchor":"bottom","yref":"paper"}],"title":{"text":"\u77e5\u8bc6\u56fe\u8c31\u7edf\u8ba1\u5206\u6790"},"showlegend":false,"height":800},                        {"responsive": true}                    )                };            </script>        </div>

                </div>
            </div>

            <div class="section">
                <h2>📍 实体列表</h2>
                <div class="entity-list">
                    <span class="tag">王五在百</span><span class="tag">李四担任</span><span class="tag">王五在百度公司</span><span class="tag">张三是阿</span><span class="tag">张三是阿里巴巴公司</span><span class="tag">李四担任腾讯公司</span><span class="tag">CEO</span>
                    
                </div>
            </div>

            <div class="section">
                <h2>🔗 关系类型</h2>
                <div class="relation-list">
                    <span class="tag relation-tag">担任</span><span class="tag relation-tag">工作于</span>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
        