#!/usr/bin/env python3
"""
测试知识图谱构建功能，验证division by zero错误是否已修复
"""

import asyncio
import json
from kg_server import build_knowledge_graph_tool

async def test_empty_data():
    """测试空数据的情况，这之前会导致division by zero错误"""
    # 测试空文本数据
    empty_data = ""

    try:
        result = await build_knowledge_graph_tool({"text": empty_data})
        print("✅ 空数据测试通过")
        # 解析结果
        result_text = result[0].text if result else "{}"
        result_data = json.loads(result_text)
        print(f"结果: {json.dumps(result_data, indent=2, ensure_ascii=False)}")
        return True
    except Exception as e:
        print(f"❌ 空数据测试失败: {e}")
        return False

async def test_minimal_data():
    """测试最小数据集"""
    # 测试最小文本数据
    minimal_data = "这是一个测试。"

    try:
        result = await build_knowledge_graph_tool({"text": minimal_data})
        print("✅ 最小数据测试通过")
        # 解析结果
        result_text = result[0].text if result else "{}"
        result_data = json.loads(result_text)
        print(f"结果: {json.dumps(result_data, indent=2, ensure_ascii=False)}")
        return True
    except Exception as e:
        print(f"❌ 最小数据测试失败: {e}")
        return False

async def main():
    """运行所有测试"""
    print("🧪 开始测试知识图谱构建功能...")

    tests = [
        ("空数据测试", test_empty_data),
        ("最小数据测试", test_minimal_data)
    ]

    results = []
    for test_name, test_func in tests:
        print(f"\n📋 运行 {test_name}...")
        result = await test_func()
        results.append((test_name, result))

    print("\n📊 测试结果汇总:")
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")

    all_passed = all(result for _, result in results)
    if all_passed:
        print("\n🎉 所有测试都通过了！division by zero错误已修复。")
    else:
        print("\n⚠️ 部分测试失败，需要进一步检查。")

if __name__ == "__main__":
    asyncio.run(main())
